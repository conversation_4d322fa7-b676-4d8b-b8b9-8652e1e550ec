-- 销售出库明细账表结构
-- 基于20250106销售出库明细账.xlsx文件创建

CREATE DATABASE IF NOT EXISTS sales_management 
CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE sales_management;

-- 销售出库明细表
CREATE TABLE sales_outbound_detail (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    
    -- 订单基本信息
    order_no VARCHAR(50) NOT NULL COMMENT '订单编号',
    original_order_no VARCHAR(50) COMMENT '原始单号',
    sub_original_order_no VARCHAR(50) COMMENT '子单原始单号',
    original_sub_order_no VARCHAR(50) COMMENT '原始子订单号',
    order_type VARCHAR(20) COMMENT '订单类型',
    payment_account VARCHAR(50) COMMENT '支付账号',
    
    -- 出库信息
    outbound_order_no VARCHAR(50) NOT NULL COMMENT '出库单编号',
    warehouse VARCHAR(50) COMMENT '仓库',
    warehouse_type VARCHAR(20) COMMENT '仓库类型',
    store VARCHAR(100) COMMENT '店铺',
    outbound_order_status VARCHAR(20) COMMENT '出库单状态',
    outbound_status VARCHAR(50) COMMENT '出库状态',
    sorting_sequence INT COMMENT '分拣序号',
    
    -- 商品信息
    merchant_code VARCHAR(100) COMMENT '商家编码',
    product_code VARCHAR(50) COMMENT '货品编号',
    product_name VARCHAR(200) COMMENT '货品名称',
    product_short_name VARCHAR(100) COMMENT '货品简称',
    brand VARCHAR(50) COMMENT '品牌',
    category VARCHAR(100) COMMENT '分类',
    spec_code VARCHAR(50) COMMENT '规格码',
    spec_name VARCHAR(100) COMMENT '规格名称',
    platform_product_name VARCHAR(200) COMMENT '平台货品名称',
    platform_spec_name VARCHAR(100) COMMENT '平台规格名称',
    platform_product_id VARCHAR(50) COMMENT '平台货品ID',
    platform_spec_id VARCHAR(50) COMMENT '平台规格ID',
    barcode VARCHAR(50) COMMENT '条形码',
    
    -- 数量和价格信息
    product_quantity INT NOT NULL COMMENT '货品数量',
    product_original_price DECIMAL(10,2) COMMENT '货品原单价',
    product_original_total_amount DECIMAL(10,2) COMMENT '货品原总金额',
    order_total_discount DECIMAL(10,2) COMMENT '订单总优惠',
    shipping_fee DECIMAL(10,2) COMMENT '邮费',
    product_deal_price DECIMAL(10,2) COMMENT '货品成交价',
    product_deal_total_price DECIMAL(10,2) COMMENT '货品成交总价',
    product_total_discount DECIMAL(10,2) COMMENT '货品总优惠',
    cod_amount DECIMAL(10,2) COMMENT '货到付款金额',
    
    -- 成本信息
    product_cost DECIMAL(10,2) COMMENT '货品成本',
    product_total_cost DECIMAL(10,2) COMMENT '货品总成本',
    fixed_cost DECIMAL(10,2) COMMENT '固定成本',
    fixed_total_cost DECIMAL(10,2) COMMENT '固定总成本',
    
    -- 支付信息
    order_payment_amount DECIMAL(10,2) COMMENT '订单支付金额',
    receivable_amount DECIMAL(10,2) COMMENT '应收金额',
    pre_refund_payment_amount DECIMAL(10,2) COMMENT '退款前支付金额',
    single_product_payment_amount DECIMAL(10,2) COMMENT '单品支付金额',
    allocated_shipping_fee DECIMAL(10,2) COMMENT '分摊邮费',
    estimated_postage DECIMAL(10,2) COMMENT '预估邮资',
    postage_cost DECIMAL(10,2) COMMENT '邮资成本',
    order_packaging_cost DECIMAL(10,2) COMMENT '订单包装成本',
    
    -- 利润信息
    order_gross_profit DECIMAL(10,2) COMMENT '订单毛利',
    gross_profit_rate DECIMAL(5,2) COMMENT '毛利率',
    order_fixed_gross_profit DECIMAL(10,2) COMMENT '订单固定毛利',
    fixed_gross_profit_rate DECIMAL(5,2) COMMENT '固定毛利率',
    
    -- 客户信息
    customer_username VARCHAR(100) COMMENT '客户网名',
    recipient_name VARCHAR(50) COMMENT '收件人',
    id_number VARCHAR(30) COMMENT '证件号码',
    delivery_area VARCHAR(100) COMMENT '收货地区',
    delivery_address VARCHAR(500) COMMENT '收货地址',
    recipient_mobile VARCHAR(20) COMMENT '收件人手机',
    recipient_phone VARCHAR(20) COMMENT '收件人电话',
    
    -- 物流信息
    logistics_company VARCHAR(100) COMMENT '物流公司',
    actual_weight DECIMAL(8,2) COMMENT '实际重量',
    estimated_weight DECIMAL(8,2) COMMENT '预估重量',
    need_invoice TINYINT(1) COMMENT '需开发票',
    
    -- 操作人员信息
    order_creator VARCHAR(50) COMMENT '制单人',
    printer VARCHAR(50) COMMENT '打单员',
    picker VARCHAR(50) COMMENT '拣货员',
    packer VARCHAR(50) COMMENT '打包员',
    inspector VARCHAR(50) COMMENT '检视员',
    salesperson VARCHAR(50) COMMENT '业务员',
    verifier VARCHAR(50) COMMENT '验货员',
    
    -- 打印信息
    print_batch VARCHAR(50) COMMENT '打印波次',
    logistics_print_status VARCHAR(20) COMMENT '物流单打印状态',
    delivery_print_status VARCHAR(20) COMMENT '发货单打印状态',
    sorting_print_status VARCHAR(20) COMMENT '分拣单打印状态',
    logistics_tracking_no VARCHAR(50) COMMENT '物流单号',
    sorting_order_no VARCHAR(50) COMMENT '分拣单编号',
    external_order_no VARCHAR(50) COMMENT '外部单号',
    
    -- 时间信息
    payment_time DATETIME COMMENT '付款时间',
    delivery_time DATETIME COMMENT '发货时间',
    order_time DATETIME COMMENT '下单时间',
    audit_time DATETIME COMMENT '审核时间',
    
    -- 其他信息
    gift_method VARCHAR(50) COMMENT '赠品方式',
    buyer_message TEXT COMMENT '买家留言',
    customer_service_note TEXT COMMENT '客服备注',
    print_note TEXT COMMENT '打印备注',
    note TEXT COMMENT '备注',
    packaging VARCHAR(100) COMMENT '包装',
    source_combo_code VARCHAR(50) COMMENT '来源组合装编码',
    split_from_combo VARCHAR(100) COMMENT '拆自组合装',
    source_combo_quantity INT COMMENT '来源组合装数量',
    volume DECIMAL(10,2) COMMENT '体积',
    distributor VARCHAR(100) COMMENT '分销商',
    distributor_code VARCHAR(50) COMMENT '分销商编号',
    distributor_original_order_no VARCHAR(50) COMMENT '分销原始单号',
    
    -- 索引
    INDEX idx_order_no (order_no),
    INDEX idx_outbound_order_no (outbound_order_no),
    INDEX idx_product_code (product_code),
    INDEX idx_store (store),
    INDEX idx_warehouse (warehouse),
    INDEX idx_delivery_time (delivery_time),
    INDEX idx_order_time (order_time),
    INDEX idx_audit_time (audit_time),
    INDEX idx_logistics_tracking_no (logistics_tracking_no),
    
    -- 创建时间和更新时间
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
    
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='销售出库明细表';

-- 创建相关的辅助表

-- 仓库表
CREATE TABLE warehouses (
    id INT AUTO_INCREMENT PRIMARY KEY,
    warehouse_name VARCHAR(50) NOT NULL UNIQUE COMMENT '仓库名称',
    warehouse_type VARCHAR(20) COMMENT '仓库类型',
    status TINYINT(1) DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='仓库表';

-- 店铺表
CREATE TABLE stores (
    id INT AUTO_INCREMENT PRIMARY KEY,
    store_name VARCHAR(100) NOT NULL UNIQUE COMMENT '店铺名称',
    platform VARCHAR(50) COMMENT '平台',
    status TINYINT(1) DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='店铺表';

-- 商品表
CREATE TABLE products (
    id INT AUTO_INCREMENT PRIMARY KEY,
    product_code VARCHAR(50) NOT NULL UNIQUE COMMENT '货品编号',
    product_name VARCHAR(200) NOT NULL COMMENT '货品名称',
    product_short_name VARCHAR(100) COMMENT '货品简称',
    brand VARCHAR(50) COMMENT '品牌',
    category VARCHAR(100) COMMENT '分类',
    barcode VARCHAR(50) COMMENT '条形码',
    status TINYINT(1) DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商品表';

-- 商品规格表
CREATE TABLE product_specs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    product_id INT NOT NULL COMMENT '商品ID',
    spec_code VARCHAR(50) COMMENT '规格码',
    spec_name VARCHAR(100) COMMENT '规格名称',
    platform_spec_id VARCHAR(50) COMMENT '平台规格ID',
    platform_spec_name VARCHAR(100) COMMENT '平台规格名称',
    status TINYINT(1) DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商品规格表';

-- 物流公司表
CREATE TABLE logistics_companies (
    id INT AUTO_INCREMENT PRIMARY KEY,
    company_name VARCHAR(100) NOT NULL UNIQUE COMMENT '物流公司名称',
    company_code VARCHAR(20) COMMENT '公司代码',
    status TINYINT(1) DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='物流公司表';
